#!/bin/bash
# 硬件信息检测模块
# 功能: 主板、CPU、内存、存储、网口信息检测
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 模块信息
# =============================================================================

MODULE_NAME="硬件信息检测模块"
MODULE_VERSION="1.0"

# =============================================================================
# 参数验证和解析
# =============================================================================

validate_parameters() {
    # 本地检测模式：检查是否为localhost
    if [[ "$1" == "localhost" ]]; then
        return 0
    fi

    # 远程检测模式：原有参数验证逻辑
    if [[ $# -ne 4 ]]; then
        log_error "参数错误"
        echo "用法: $0 <设备名称> <设备IP> <SSH用户名> <SSH密码>"
        echo "示例: $0 n1 ************* admin admin@123"
        return 1
    fi

    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    # 验证设备名称
    if [[ ! "$device_name" =~ ^(n1|n2|n3)$ ]]; then
        log_error "无效的设备名称: $device_name (支持: n1, n2, n3)"
        return 1
    fi

    # 验证IP地址格式
    if [[ ! "$device_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        log_error "无效的IP地址格式: $device_ip"
        return 1
    fi

    # 验证用户名和密码非空
    if [[ -z "$ssh_user" ]]; then
        log_error "SSH用户名不能为空"
        return 1
    fi

    if [[ -z "$ssh_password" ]]; then
        log_error "SSH密码不能为空"
        return 1
    fi

    return 0
}

# =============================================================================
# 硬件检测功能
# =============================================================================

# 主板信息检测
get_motherboard_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo "=========================================="
    echo "主板信息检测"
    echo "=========================================="

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "设备: localhost (本地主机)"
        echo ""

        log_info "开始主板信息检测..."

        # 直接执行本地命令
        local mb_info=""
        mb_info=$(dmidecode -t baseboard 2>/dev/null)
    else
        # 远程检测模式（保持原有逻辑）
        echo "设备: $device_name ($device_ip:$ssh_port)"
        echo ""

        log_info "开始主板信息检测..."

        # 根据设备类型使用不同的命令
        local mb_info=""
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令获取硬件信息
            mb_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
        else
            # Linux系统使用标准命令
            mb_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "dmidecode -t baseboard 2>/dev/null || cat /proc/cpuinfo | head -20" 2>/dev/null)
        fi
    fi

    if [[ $? -eq 0 && -n "$mb_info" ]]; then
        echo "主板信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中动态提取信息
            local mb_data=$(echo "$mb_info" | grep -v "INFO\|ERROR\|WARN")
            local platform_info=$(echo "$mb_data" | grep "Platform" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local model_info=$(echo "$mb_data" | grep "Model" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local product_info=$(echo "$mb_data" | grep "Product" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  平台: ${platform_info:-"-"}"
            echo "  型号: ${model_info:-"-"}"
            echo "  产品: ${product_info:-"-"}"
        else
            # Linux系统从dmidecode输出中提取主板信息
            local mb_manufacturer=$(echo "$mb_info" | grep "Manufacturer:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local mb_product=$(echo "$mb_info" | grep "Product Name:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local mb_version=$(echo "$mb_info" | grep "Version:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  制造商: ${mb_manufacturer:-"Unknown"}"
            echo "  型号: ${mb_product:-"Unknown"}"
            echo "  版本: ${mb_version:-"Unknown"}"
        fi
        log_info "主板信息检测成功"
        return 0
    else
        echo "主板信息获取失败"
        log_error "主板信息检测失败"
        return 1
    fi
}

# CPU信息检测
get_cpu_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "CPU信息检测"
    echo "=========================================="

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始CPU信息检测..."

        # 直接执行本地命令
        local cpu_info=""
        cpu_info=$(cat /proc/cpuinfo | grep -E 'model name|cpu cores|processor|cpu MHz|BogoMIPS' | head -15 2>/dev/null)
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始CPU信息检测..."

        local cpu_info=""
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令
            cpu_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
        else
            # Linux系统
            cpu_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "cat /proc/cpuinfo | grep -E 'model name|cpu cores|processor|cpu MHz|BogoMIPS' | head -15" 2>/dev/null)
        fi
    fi

    if [[ $? -eq 0 && -n "$cpu_info" ]]; then
        echo "CPU信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中尝试提取CPU相关信息
            local cpu_data=$(echo "$cpu_info" | grep -v "INFO\|ERROR\|WARN")

            # 尝试从输出中查找CPU相关信息（通常AFW3000的show ver不包含详细CPU信息）
            local cpu_model=$(echo "$cpu_data" | grep -i "cpu\|processor" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local cpu_cores=$(echo "$cpu_data" | grep -i "core\|核" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local cpu_freq=$(echo "$cpu_data" | grep -i "freq\|mhz\|频率" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  型号: ${cpu_model:-"-"}"
            echo "  核数: ${cpu_cores:-"-"}"
            echo "  频率: ${cpu_freq:-"-"}"
        else
            # 从SSH输出中提取CPU信息，过滤掉日志行
            local cpu_data=$(echo "$cpu_info" | grep -v "INFO\|ERROR\|WARN" | grep -E "model name|cpu cores|cpu MHz|processor|BogoMIPS")

            # 获取CPU型号
            local cpu_model=$(echo "$cpu_data" | grep "model name" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            # 获取CPU核心数（如果没有cpu cores字段，则统计processor数量）
            local cpu_cores=$(echo "$cpu_data" | grep "cpu cores" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            if [[ -z "$cpu_cores" ]]; then
                cpu_cores=$(echo "$cpu_data" | grep "processor" | wc -l)
            fi

            # 获取CPU频率（优先使用cpu MHz，如果没有则使用BogoMIPS）
            local cpu_freq=$(echo "$cpu_data" | grep "cpu MHz" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            if [[ -z "$cpu_freq" ]]; then
                cpu_freq=$(echo "$cpu_data" | grep "BogoMIPS" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                if [[ -n "$cpu_freq" ]]; then
                    cpu_freq="${cpu_freq} (BogoMIPS)"
                fi
            else
                cpu_freq="${cpu_freq}MHz"
            fi

            # 显示CPU信息
            echo "  型号: ${cpu_model:-未知}"
            echo "  核数: ${cpu_cores:-未知}核"
            echo "  频率: ${cpu_freq:-未知}"
        fi
        log_info "CPU信息检测成功"
        return 0
    else
        echo "CPU信息获取失败"
        log_error "CPU信息检测失败"
        return 1
    fi
}

# 内存信息检测
get_memory_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "内存信息检测"
    echo "=========================================="

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始内存信息检测..."

        # 直接执行本地命令
        local mem_info=""
        mem_info=$(free -h && cat /proc/meminfo | head -5 && echo '=== Memory Details ===' && dmidecode -t memory 2>/dev/null)
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始内存信息检测..."

        local mem_info=""
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令
            mem_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
        else
            # Linux系统 - 新增dmidecode -t memory命令获取详细内存配置信息
            mem_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "free -h && cat /proc/meminfo | head -5 && echo '=== Memory Details ===' && dmidecode -t memory 2>/dev/null" 2>/dev/null)
        fi
    fi

    if [[ $? -eq 0 && -n "$mem_info" ]]; then
        echo "内存信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中尝试提取内存信息
            local mem_data=$(echo "$mem_info" | grep -v "INFO\|ERROR\|WARN")

            # 尝试从输出中查找内存相关信息（通常AFW3000的show ver不包含详细内存信息）
            local total_mem=$(echo "$mem_data" | grep -i "memory\|mem\|内存" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local mem_type=$(echo "$mem_data" | grep -i "ddr\|ram" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  └── 总容量：${total_mem:-"-"}"
            echo "  内存条列表:"
            if [[ -n "$total_mem" && "$total_mem" != "-" ]]; then
                echo "    └── 内存条1: 型号=${mem_type:-"-"}, 容量=$total_mem, 类型=${mem_type:-"-"}"
            else
                echo "    └── 无内存条信息"
            fi
        else
            # Linux系统从实际输出中提取内存信息
            local mem_data=$(echo "$mem_info" | grep -v "INFO\|ERROR\|WARN")
            local available_mem=$(echo "$mem_data" | grep "MemTotal" | awk '{printf "%.0fMB", $2/1024}' 2>/dev/null)
            local mem_available=$(echo "$mem_data" | grep "MemAvailable" | awk '{printf "%.0fMB", $2/1024}' 2>/dev/null)

            # 尝试从dmidecode获取物理内存容量
            local physical_mem=""
            local dmidecode_data=$(echo "$mem_info" | grep -A 100 "=== Memory Details ===")
            if [[ -n "$dmidecode_data" ]]; then
                # 从dmidecode输出中提取物理内存大小，使用更精确的匹配
                local mem_size=$(echo "$dmidecode_data" | grep -E "^[[:space:]]*Size:[[:space:]]*[0-9]+" | head -1 | sed 's/^[[:space:]]*Size:[[:space:]]*//' | sed 's/[[:space:]]*$//')
                if [[ -n "$mem_size" && "$mem_size" != "No Module Installed" && "$mem_size" != "No" ]]; then
                    physical_mem="$mem_size"
                else
                    # 如果dmidecode解析失败，尝试从内存条列表中提取
                    local mem_from_list=$(echo "$mem_info" | grep -E "容量=[0-9]+ MB" | head -1 | sed 's/.*容量=//' | sed 's/ MB.*//' | sed 's/$/MB/')
                    if [[ -n "$mem_from_list" ]]; then
                        physical_mem="$mem_from_list"
                    else
                        physical_mem="${available_mem:-"-"}"
                    fi
                fi
            else
                # 如果没有dmidecode数据，尝试从内存条列表中提取
                local mem_from_list=$(echo "$mem_info" | grep -E "容量=[0-9]+ MB" | head -1 | sed 's/.*容量=//' | sed 's/ MB.*//' | sed 's/$/MB/')
                if [[ -n "$mem_from_list" ]]; then
                    physical_mem="$mem_from_list"
                else
                    physical_mem="${available_mem:-"-"}"
                fi
            fi

            # 计算内存总容量（从内存条列表中统计）
            local total_memory_capacity=""
            # 从内存条列表中提取容量信息并求和
            local memory_list_data=$(echo "$mem_info" | grep -E "容量=[0-9]+ [GM]B")
            if [[ -n "$memory_list_data" ]]; then
                local total_mb=0
                local memory_count=0
                while IFS= read -r line; do
                    if [[ "$line" =~ 容量=([0-9]+)[[:space:]]*([GM]B) ]]; then
                        local value="${BASH_REMATCH[1]}"
                        local unit="${BASH_REMATCH[2]}"
                        memory_count=$((memory_count + 1))
                        if [[ "$unit" == "GB" ]]; then
                            total_mb=$((total_mb + value * 1024))
                        elif [[ "$unit" == "MB" ]]; then
                            total_mb=$((total_mb + value))
                        fi
                    fi
                done <<<"$memory_list_data"

                # 如果总容量大于1024MB，转换为GB显示
                if [[ $total_mb -gt 1024 ]]; then
                    local total_gb=$((total_mb / 1024))
                    total_memory_capacity="${total_gb}GB"
                else
                    total_memory_capacity="${total_mb}MB"
                fi
            else
                total_memory_capacity="${available_mem:-"-"}"
            fi

            echo "  └── 总容量：${total_memory_capacity:-"-"}"
            echo "  内存条列表:"

            # 尝试从dmidecode输出中提取详细内存条信息
            local dmidecode_data=$(echo "$mem_data" | sed -n '/=== Memory Details ===/,$p')
            if [[ -n "$dmidecode_data" ]]; then
                # 解析dmidecode输出中的内存条信息
                local memory_devices=$(echo "$dmidecode_data" | grep -A 20 "Memory Device" | grep -E "Size:|Type:|Speed:|Manufacturer:|Part Number:" | sed 's/^[[:space:]]*//')

                if [[ -n "$memory_devices" ]]; then
                    local device_count=1
                    local current_size=""
                    local current_type=""
                    local current_speed=""
                    local current_manufacturer=""
                    local current_part=""
                    local memory_list=()

                    while IFS= read -r line; do
                        if [[ "$line" =~ Size:[[:space:]]*(.+) ]]; then
                            current_size="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Type:[[:space:]]*(.+) ]]; then
                            current_type="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Speed:[[:space:]]*(.+) ]]; then
                            current_speed="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Manufacturer:[[:space:]]*(.+) ]]; then
                            current_manufacturer="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Part\ Number:[[:space:]]*(.+) ]]; then
                            current_part="${BASH_REMATCH[1]}"

                            # 当获取到Part Number时，表示一个内存条信息收集完成
                            if [[ "$current_size" != "No Module Installed" && -n "$current_size" ]]; then
                                memory_list+=("内存条${device_count}: 型号=${current_part:-"-"}, 容量=${current_size:-"-"}, 类型=${current_type:-"-"}, 速度=${current_speed:-"-"}, 厂商=${current_manufacturer:-"-"}")
                                device_count=$((device_count + 1))
                            fi

                            # 重置变量
                            current_size=""
                            current_type=""
                            current_speed=""
                            current_manufacturer=""
                            current_part=""
                        fi
                    done <<<"$memory_devices"

                    # 输出内存条列表，使用正确的树状结构
                    for i in "${!memory_list[@]}"; do
                        if [[ $i -eq $((${#memory_list[@]} - 1)) ]]; then
                            echo "    └── ${memory_list[i]}"
                        else
                            echo "    ├── ${memory_list[i]}"
                        fi
                    done

                    # 如果没有找到任何内存条，显示基本信息
                    if [[ $device_count -eq 1 ]]; then
                        if [[ -n "$physical_mem" && "$physical_mem" != "-" ]]; then
                            echo "    └── 内存条1: 型号=-, 容量=$physical_mem, 类型=-"
                        else
                            echo "    └── 无内存条信息"
                        fi
                    fi
                else
                    # dmidecode没有返回有效数据，使用基本信息
                    if [[ -n "$physical_mem" && "$physical_mem" != "-" ]]; then
                        echo "    └── 内存条1: 型号=-, 容量=$physical_mem, 类型=-"
                    else
                        echo "    └── 无内存条信息"
                    fi
                fi
            else
                # 没有dmidecode数据，使用基本信息
                if [[ -n "$physical_mem" && "$physical_mem" != "-" ]]; then
                    echo "    └── 内存条1: 型号=-, 容量=$physical_mem, 类型=-"
                else
                    echo "    └── 无内存条信息"
                fi
            fi
        fi
        log_info "内存信息检测成功"
        return 0
    else
        echo "内存信息获取失败"
        log_error "内存信息检测失败"
        return 1
    fi
}

# 存储信息检测
get_storage_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "存储信息检测"
    echo "=========================================="

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始存储信息检测..."

        # 直接执行本地命令
        local storage_info=""
        storage_info=$(df -h && lsblk 2>/dev/null && echo '=== Hardware Disk Info ===' && (hwinfo --disk 2>/dev/null || echo 'hwinfo command not available') && echo '=== Disk Partitions ===' && fdisk -l 2>/dev/null)
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始存储信息检测..."

        local storage_info=""
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令
            storage_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
        else
            # Linux系统 - 新增hwinfo --disk和fdisk -l命令获取详细存储信息
            # 使用条件执行确保hwinfo不可用时不影响整体执行
            storage_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "df -h && lsblk 2>/dev/null && echo '=== Hardware Disk Info ===' && (hwinfo --disk 2>/dev/null || echo 'hwinfo command not available') && echo '=== Disk Partitions ===' && fdisk -l 2>/dev/null" 2>/dev/null)
        fi
    fi

    if [[ $? -eq 0 && -n "$storage_info" ]]; then
        echo "存储信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中尝试提取存储信息
            local storage_data=$(echo "$storage_info" | grep -v "INFO\|ERROR\|WARN")

            # 尝试从输出中查找存储相关信息（通常AFW3000的show ver不包含详细存储信息）
            local total_storage=$(echo "$storage_data" | grep -i "storage\|disk\|flash\|存储" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local flash_info=$(echo "$storage_data" | grep -i "flash" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local disk_info=$(echo "$storage_data" | grep -i "disk\|ssd\|hdd" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  └── 总容量：${total_storage:-"-"}"
            echo "  硬盘列表:"
            local storage_items=()
            if [[ -n "$flash_info" && "$flash_info" != "-" ]]; then
                storage_items+=("Flash存储: $flash_info")
            fi
            if [[ -n "$disk_info" && "$disk_info" != "-" ]]; then
                storage_items+=("磁盘存储: $disk_info")
            fi

            if [[ ${#storage_items[@]} -gt 0 ]]; then
                for i in "${!storage_items[@]}"; do
                    if [[ $i -eq $((${#storage_items[@]} - 1)) ]]; then
                        echo "    └── ${storage_items[i]}"
                    else
                        echo "    ├── ${storage_items[i]}"
                    fi
                done
            else
                echo "    └── 无存储设备信息"
            fi
        else
            # Linux系统从实际输出中提取存储信息
            local storage_data=$(echo "$storage_info" | grep -v "INFO\|ERROR\|WARN")

            # 从df命令输出中提取根分区信息（可用容量）
            local available_storage=$(echo "$storage_data" | grep "/$" | awk '{print $2}' | head -1)
            local root_device=$(echo "$storage_data" | grep "/$" | awk '{print $1}' | head -1)

            # 从lsblk输出中提取物理硬盘总容量
            local physical_storage=""
            local lsblk_data=$(echo "$storage_data" | grep -E "^[a-z]+[[:space:]]+[0-9]+:[0-9]+[[:space:]]+[0-9]+[[:space:]]+[0-9.]+[KMGT]")
            if [[ -n "$lsblk_data" ]]; then
                # 提取主要硬盘的容量（通常是sda或nvme0n1）
                local main_disk_size=$(echo "$lsblk_data" | grep -E "^(sda|nvme0n1|mmcblk0)" | head -1 | awk '{print $4}')
                if [[ -n "$main_disk_size" ]]; then
                    physical_storage="$main_disk_size"
                else
                    physical_storage="${available_storage:-"-"}"
                fi
            else
                physical_storage="${available_storage:-"-"}"
            fi

            # 计算存储总容量（所有物理硬盘容量的总和）
            local total_storage_capacity=""
            local lsblk_data=$(echo "$storage_data" | grep -E "^[a-z]+[[:space:]]+[0-9]+:[0-9]+[[:space:]]+[0-9]+[[:space:]]+[0-9.]+[KMGT]")
            if [[ -n "$lsblk_data" ]]; then
                # 提取所有物理硬盘的容量（排除分区和虚拟设备）
                local disk_sizes=($(echo "$lsblk_data" | grep -E "^(sd[a-z]|nvme[0-9]+n[0-9]+|mmcblk[0-9]+)[[:space:]]" | awk '{print $4}'))
                if [[ ${#disk_sizes[@]} -gt 0 ]]; then
                    local total_gb=0
                    for size in "${disk_sizes[@]}"; do
                        local value=$(echo "$size" | grep -o '[0-9.]*' | cut -d'.' -f1)
                        local size_unit=$(echo "$size" | grep -o '[KMGT]')
                        case "$size_unit" in
                        "T") total_gb=$((total_gb + value * 1024)) ;;
                        "G") total_gb=$((total_gb + value)) ;;
                        "M") total_gb=$((total_gb + value / 1024)) ;;
                        "K") total_gb=$((total_gb + value / 1024 / 1024)) ;;
                        esac
                    done

                    # 格式化显示
                    if [[ $total_gb -gt 1024 ]]; then
                        local total_tb=$((total_gb / 1024))
                        total_storage_capacity="${total_tb}TB"
                    else
                        total_storage_capacity="${total_gb}GB"
                    fi
                else
                    total_storage_capacity="${available_storage:-"-"}"
                fi
            else
                total_storage_capacity="${available_storage:-"-"}"
            fi

            echo "  └── 总容量：${total_storage_capacity:-"-"}"
            echo "  硬盘列表:"

            # 尝试从hwinfo和fdisk输出中提取详细硬盘信息
            local hwinfo_data=$(echo "$storage_data" | sed -n '/=== Hardware Disk Info ===/,/=== Disk Partitions ===/p')
            local fdisk_data=$(echo "$storage_data" | sed -n '/=== Disk Partitions ===/,$p')

            local disk_count=1
            local found_disks=false
            local disk_list=()

            # 首先尝试从fdisk -l输出中提取磁盘信息
            if [[ -n "$fdisk_data" ]]; then
                local disk_lines=$(echo "$fdisk_data" | grep "^Disk /dev/" | grep -v "/dev/ram" | head -5)

                while IFS= read -r line; do
                    if [[ -n "$line" ]]; then
                        # 解析fdisk输出: Disk /dev/sda: 20 GiB, 21474836480 bytes, 41943040 sectors
                        if [[ "$line" =~ Disk[[:space:]]+(/dev/[^:]+):[[:space:]]*([^,]+) ]]; then
                            local disk_device="${BASH_REMATCH[1]}"
                            local disk_size="${BASH_REMATCH[2]}"

                            # 尝试从fdisk输出中获取磁盘型号信息
                            local disk_model=""
                            local disk_type=""

                            # 查找Disk model行
                            local model_line=$(echo "$fdisk_data" | grep -A 2 "^Disk $disk_device:" | grep "Disk model:" | head -1)
                            if [[ -n "$model_line" ]]; then
                                disk_model=$(echo "$model_line" | sed 's/Disk model:[[:space:]]*//' | sed 's/[[:space:]]*$//')
                            fi

                            # 判断磁盘类型（基于设备名称和型号）
                            if [[ "$disk_device" =~ mmcblk ]]; then
                                disk_type="MMC/eMMC"
                            elif [[ "$disk_model" =~ SSD|ssd ]]; then
                                disk_type="SSD"
                            elif [[ "$disk_model" =~ HDD|hdd ]]; then
                                disk_type="HDD"
                            else
                                # 尝试从hwinfo中获取类型信息（如果可用）
                                if [[ -n "$hwinfo_data" && ! "$hwinfo_data" =~ "hwinfo command not available" ]]; then
                                    local device_section=$(echo "$hwinfo_data" | grep -A 20 "$disk_device" | head -20)
                                    if echo "$device_section" | grep -qi "ssd\|solid"; then
                                        disk_type="SSD"
                                    elif echo "$device_section" | grep -qi "hdd\|hard"; then
                                        disk_type="HDD"
                                    else
                                        disk_type="未知"
                                    fi
                                else
                                    disk_type="-"
                                fi
                            fi

                            disk_list+=("硬盘${disk_count}: 设备=$disk_device, 容量=$disk_size, 类型=${disk_type:-"-"}, 型号=${disk_model:-"-"}")
                            disk_count=$((disk_count + 1))
                            found_disks=true
                        fi
                    fi
                done <<<"$disk_lines"
            fi

            # 如果fdisk没有找到磁盘信息，尝试从lsblk输出中提取
            if [[ "$found_disks" == false ]]; then
                local lsblk_disks=$(echo "$storage_data" | grep -E "disk" | head -3)

                while IFS= read -r line; do
                    if [[ -n "$line" ]]; then
                        local disk_name=$(echo "$line" | awk '{print $1}')
                        local disk_size=$(echo "$line" | awk '{print $4}')

                        if [[ -n "$disk_name" && -n "$disk_size" ]]; then
                            disk_list+=("硬盘${disk_count}: 设备=/dev/$disk_name, 容量=$disk_size, 类型=-, 型号=-")
                            disk_count=$((disk_count + 1))
                            found_disks=true
                        fi
                    fi
                done <<<"$lsblk_disks"
            fi

            # 输出硬盘列表，使用正确的树状结构
            if [[ ${#disk_list[@]} -gt 0 ]]; then
                for i in "${!disk_list[@]}"; do
                    if [[ $i -eq $((${#disk_list[@]} - 1)) ]]; then
                        echo "    └── ${disk_list[i]}"
                    else
                        echo "    ├── ${disk_list[i]}"
                    fi
                done
            else
                # 如果仍然没有找到磁盘信息，显示基本信息
                if [[ -n "$root_device" && "$root_device" != "-" ]]; then
                    echo "    └── 硬盘1: 设备=$root_device, 容量=${available_storage:-"-"}, 类型=-, 型号=-"
                else
                    echo "    └── 无硬盘信息"
                fi
            fi
        fi
        log_info "存储信息检测成功"
        return 0
    else
        echo "存储信息获取失败"
        log_error "存储信息检测失败"
        return 1
    fi
}

# 网口信息检测
get_network_interface_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "网口信息检测"
    echo "=========================================="

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始网口信息检测..."

        # 直接执行本地命令
        local nic_info=""
        nic_info=$(ip addr show && ethtool eth0 2>/dev/null | head -10)
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始网口信息检测..."

        local nic_info=""
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show interface命令
            nic_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show interface" 2>/dev/null)
        else
            # Linux系统
            nic_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "ip addr show && ethtool eth0 2>/dev/null | head -10" 2>/dev/null)
        fi
    fi

    if [[ $? -eq 0 && -n "$nic_info" ]]; then
        echo "网口信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show interface输出中动态提取网口信息
            local interface_data=$(echo "$nic_info" | grep -v "INFO\|ERROR\|WARN" | grep -E "ge[0-9]|bvi[0-9]")
            local interface_count=$(echo "$interface_data" | grep "ge[0-9]" | wc -l)

            echo "  网口总数: ${interface_count}个千兆网口"
            echo "  网口列表:"

            # 解析每个网口的信息，按照设计文档格式显示
            local configured_interfaces=()
            local unconfigured_interfaces=()

            # 使用数组方式处理接口数据，避免while循环的问题
            IFS=$'\n' read -d '' -r -a interface_lines <<<"$interface_data"

            for line in "${interface_lines[@]}"; do
                if [[ "$line" =~ ge[0-9] ]]; then
                    local interface_name=$(echo "$line" | awk '{print $1}')
                    local ip_addr=$(echo "$line" | awk '{print $2}')
                    local mac_addr=$(echo "$line" | awk '{print $5}')
                    local link_state=$(echo "$line" | awk '{print $4}')

                    # 动态获取接口速率信息
                    local interface_speed="-"
                    local detailed_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show interface $interface_name" 2>/dev/null)
                    if [[ $? -eq 0 && -n "$detailed_info" ]]; then
                        # 从详细输出中提取速率信息
                        local speed_line=$(echo "$detailed_info" | grep -E "Speed:" | head -1)
                        if [[ -n "$speed_line" ]]; then
                            local speed_value=$(echo "$speed_line" | sed 's/.*Speed:[[:space:]]*\([0-9]*\).*/\1/')
                            if [[ -n "$speed_value" && "$speed_value" =~ ^[0-9]+$ ]]; then
                                interface_speed="${speed_value}Mbps"
                            fi
                        fi
                    fi

                    if [[ "$ip_addr" == "-" ]]; then
                        unconfigured_interfaces+=("$interface_name")
                    else
                        configured_interfaces+=("$interface_name: IP=$ip_addr, MAC=$mac_addr, 速率=$interface_speed")
                    fi
                fi
            done

            # 显示已配置的网口
            for i in "${!configured_interfaces[@]}"; do
                if [[ $i -eq $((${#configured_interfaces[@]} - 1)) && ${#unconfigured_interfaces[@]} -eq 0 ]]; then
                    # 如果是最后一个且没有未配置的网口，使用└──
                    echo "    └── ${configured_interfaces[i]}"
                else
                    echo "    ├── ${configured_interfaces[i]}"
                fi
            done

            # 显示未配置的网口
            if [[ ${#unconfigured_interfaces[@]} -gt 0 ]]; then
                local unconfigured_list=""
                for interface in "${unconfigured_interfaces[@]}"; do
                    if [[ -z "$unconfigured_list" ]]; then
                        unconfigured_list="$interface"
                    else
                        unconfigured_list="$unconfigured_list, $interface"
                    fi
                done
                echo "    └── $unconfigured_list: 未配置"
            fi
        else
            # Linux系统从实际输出中提取网口信息
            local nic_data=$(echo "$nic_info" | grep -v "INFO\|ERROR\|WARN")

            # 从ip addr show输出中提取网口信息
            local interfaces=$(echo "$nic_data" | grep -E "^[0-9]+:" | awk '{print $2}' | sed 's/:$//' | head -5)
            local interface_count=$(echo "$interfaces" | wc -l)

            echo "  网口总数: ${interface_count}个接口"
            echo "  网口列表:"

            # 解析每个网口的信息
            local interface_lines=()
            while IFS= read -r interface; do
                if [[ -n "$interface" ]]; then
                    # 提取IP地址
                    local ip_addr=$(echo "$nic_data" | grep -A 5 "$interface" | grep "inet " | awk '{print $2}' | head -1)
                    # 提取MAC地址
                    local mac_addr=$(echo "$nic_data" | grep -A 5 "$interface" | grep "link/ether" | awk '{print $2}' | head -1)

                    if [[ -n "$ip_addr" ]]; then
                        interface_lines+=("$interface: IP=$ip_addr, MAC=${mac_addr:-"-"}, 速率=-")
                    else
                        interface_lines+=("$interface: 未配置IP, MAC=${mac_addr:-"-"}, 速率=-")
                    fi
                fi
            done <<<"$interfaces"

            # 显示网口信息，使用正确的树状结构
            for i in "${!interface_lines[@]}"; do
                if [[ $i -eq $((${#interface_lines[@]} - 1)) ]]; then
                    echo "    └── ${interface_lines[i]}"
                else
                    echo "    ├── ${interface_lines[i]}"
                fi
            done
        fi
        log_info "网口信息检测成功"
        return 0
    else
        echo "网口信息获取失败"
        log_error "网口信息检测失败"
        return 1
    fi
}

# =============================================================================
# 主检测流程
# =============================================================================

run_hardware_check() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    # 从配置中获取SSH端口
    local ssh_port="22"
    if [[ -f "config/devices.conf" ]]; then
        source "config/devices.conf"
        local port_var="${device_name^^}_PORT"
        ssh_port="${!port_var:-22}"
    fi

    echo "=========================================="
    echo "$MODULE_NAME v$MODULE_VERSION"
    echo "=========================================="
    echo "检测时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "目标设备: $device_name"
    echo "设备地址: $device_ip:$ssh_port"
    echo ""

    local overall_success=true
    local success_count=0
    local total_count=5

    # 1. 主板信息检测
    if get_motherboard_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 2. CPU信息检测
    if get_cpu_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 3. 内存信息检测
    if get_memory_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 4. 存储信息检测
    if get_storage_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 5. 网口信息检测
    if get_network_interface_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    echo ""
    echo "=========================================="
    echo "硬件检测完成"
    echo "=========================================="
    echo "检测项目: $total_count"
    echo "成功项目: $success_count"
    echo "失败项目: $((total_count - success_count))"

    if [[ "$overall_success" == true ]]; then
        echo "检测结果: 硬件信息获取完整"
        log_info "硬件检测全部通过"
        return 0
    else
        echo "检测结果: ⚠ 硬件信息获取不完整"
        log_warn "硬件检测存在问题"
        return 1
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 验证参数
    if ! validate_parameters "$@"; then
        exit 1
    fi

    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    # 执行硬件检测
    if run_hardware_check "$device_name" "$device_ip" "$ssh_user" "$ssh_password"; then
        exit 0
    else
        exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'log_error "硬件检测模块异常退出"; exit 1' ERR

# 调用主函数
main "$@"
